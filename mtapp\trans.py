import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import pandas as pd
import numpy as np
from datetime import datetime
import os
import traceback
import hashlib
import json

class MetaAdsTransformerPro:
    def __init__(self, root):
        self.root = root
        self.root.title("Meta Ads Data Transformer Pro - Summary Validated")
        self.root.geometry("1600x1000")
        self.root.configure(bg='#f0f0f0')
        
        # Data storage
        self.raw_data = None
        self.summary_row = None
        self.granular_data = None
        self.cleaned_data = None
        self.monthly_summary = None
        self.daily_aggregated = None
        self.airtable_export = None
        self.data_quality_report = {}
        self.validation_errors = []
        self.accuracy_check = {}

        # NEW: Ad Performance Analyzer data
        self.ad_performance_table = None
        self.ad_insights = None
        self.ad_validation = None

        # App metadata
        self.app_version = "2.2.0"
        self.processing_timestamp = None
        
        self.setup_ui()
    
    def setup_ui(self):
        # Configure modern styling
        style = ttk.Style()
        style.theme_use('clam')

        # Define modern color palette
        self.colors = {
            'primary': '#2563eb',      # Blue
            'success': '#059669',      # Green
            'warning': '#d97706',      # Orange
            'danger': '#dc2626',       # Red
            'secondary': '#6b7280',    # Gray
            'light': '#f8fafc',        # Light gray
            'dark': '#1e293b',         # Dark gray
            'white': '#ffffff'
        }

        # Configure custom styles with modern design
        style.configure('Title.TLabel', font=('Segoe UI', 24, 'bold'), foreground=self.colors['dark'])
        style.configure('Subtitle.TLabel', font=('Segoe UI', 11), foreground=self.colors['secondary'])
        style.configure('Success.TLabel', foreground=self.colors['success'], font=('Segoe UI', 11, 'bold'))
        style.configure('Warning.TLabel', foreground=self.colors['warning'], font=('Segoe UI', 11, 'bold'))
        style.configure('Error.TLabel', foreground=self.colors['danger'], font=('Segoe UI', 11, 'bold'))
        style.configure('Primary.TLabel', foreground=self.colors['primary'], font=('Segoe UI', 11, 'bold'))
        style.configure('Card.TFrame', relief='solid', borderwidth=1)
        style.configure('Primary.TButton', font=('Segoe UI', 10, 'bold'))

        # Main container with modern padding
        main_frame = ttk.Frame(self.root, padding="20")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        # Configure responsive grid - more compact layout
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(0, weight=1)  # Sidebar
        main_frame.columnconfigure(1, weight=3)  # Main content area
        main_frame.rowconfigure(1, weight=1)     # Single main content row

        # Modern header section
        self.setup_header(main_frame)

        # Main content area with card-based layout
        self.setup_main_content(main_frame)

        # Modern status bar
        self.setup_status_bar(main_frame)

    def setup_header(self, parent):
        """Setup modern header with title and branding"""
        header_frame = ttk.Frame(parent)
        header_frame.grid(row=0, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 30))
        header_frame.columnconfigure(1, weight=1)

        # App icon/logo area (placeholder)
        icon_frame = ttk.Frame(header_frame, width=60, height=60)
        icon_frame.grid(row=0, column=0, rowspan=2, padx=(0, 20))
        icon_frame.grid_propagate(False)

        # Title and subtitle
        title_label = ttk.Label(header_frame, text="Meta Ads Data Transformer Pro",
                               style='Title.TLabel')
        title_label.grid(row=0, column=1, sticky=tk.W)

        subtitle_label = ttk.Label(header_frame,
                                  text=f"v{self.app_version} • Advanced validation & deduplication-aware processing",
                                  style='Subtitle.TLabel')
        subtitle_label.grid(row=1, column=1, sticky=tk.W)

    def setup_main_content(self, parent):
        """Setup main content area with compact, efficient layout"""
        # Left sidebar - Controls (narrower)
        self.setup_control_sidebar(parent)

        # Right main area - Combined dashboard
        self.setup_main_dashboard(parent)

    def setup_status_bar(self, parent):
        """Setup modern status bar with progress indicator"""
        status_frame = ttk.Frame(parent, style='Card.TFrame', padding="10")
        status_frame.grid(row=2, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(10, 0))
        status_frame.columnconfigure(0, weight=1)

        # Status text
        self.status_var = tk.StringVar()
        self.status_var.set("🚀 Ready - Load a Meta Ads CSV file to begin validation and transformation")
        status_label = ttk.Label(status_frame, textvariable=self.status_var,
                                font=('Segoe UI', 10))
        status_label.grid(row=0, column=0, sticky=tk.W)

        # Progress bar with modern styling
        self.progress_var = tk.DoubleVar()
        self.progress_bar = ttk.Progressbar(status_frame, variable=self.progress_var,
                                           maximum=100, length=300, mode='determinate')
        self.progress_bar.grid(row=0, column=1, padx=(20, 0))
    
    def setup_control_sidebar(self, parent):
        """Setup compact control sidebar"""
        sidebar_frame = ttk.Frame(parent, style='Card.TFrame', padding="15")
        sidebar_frame.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), padx=(0, 15))
        sidebar_frame.configure(width=280)

        # File Operations Card
        file_card = self.create_card(sidebar_frame, "📁 File Operations", 0)

        load_btn = ttk.Button(file_card, text="Load Meta Ads CSV",
                             command=self.load_csv, style='Primary.TButton')
        load_btn.pack(fill=tk.X, pady=(0, 10))

        # File info display
        self.file_info_var = tk.StringVar()
        self.file_info_var.set("No file loaded")
        file_info_label = ttk.Label(file_card, textvariable=self.file_info_var,
                                   font=('Segoe UI', 9), foreground=self.colors['secondary'])
        file_info_label.pack(anchor=tk.W)

        # Processing Options Card
        options_card = self.create_card(sidebar_frame, "⚙️ Processing Options", 1)

        self.clean_data_var = tk.BooleanVar(value=True)
        self.daily_var = tk.BooleanVar(value=True)
        self.monthly_var = tk.BooleanVar(value=True)
        self.airtable_var = tk.BooleanVar(value=True)
        self.validate_summary_var = tk.BooleanVar(value=True)

        options = [
            ("Advanced Data Cleaning", self.clean_data_var),
            ("Daily Aggregation", self.daily_var),
            ("Monthly Summary", self.monthly_var),
            ("Airtable Export Format", self.airtable_var),
            ("✓ Summary Row Validation", self.validate_summary_var)
        ]

        for text, var in options:
            cb = ttk.Checkbutton(options_card, text=text, variable=var)
            cb.pack(anchor=tk.W, pady=2)

        # Process Button
        process_btn = ttk.Button(sidebar_frame, text="🔄 Process & Validate Data",
                               command=self.process_data, style='Primary.TButton')
        process_btn.pack(fill=tk.X, pady=(20, 0))

        # Export Options Card
        export_card = self.create_card(sidebar_frame, "💾 Export Options", 2)

        export_buttons = [
            ("Export for Airtable", self.export_airtable),
            ("Export Ad Performance", self.export_ad_performance),  # NEW
            ("Export Ad Insights", self.export_ad_insights),        # NEW
            ("Export Daily Data", self.export_daily),
            ("Export Monthly Summary", self.export_monthly),
            ("Export Validation Report", self.export_validation_report)
        ]

        for text, command in export_buttons:
            btn = ttk.Button(export_card, text=text, command=command)
            btn.pack(fill=tk.X, pady=2)

    def create_card(self, parent, title, row):
        """Create a modern card widget"""
        card_frame = ttk.LabelFrame(parent, text=title, padding="15")
        card_frame.pack(fill=tk.X, pady=(0, 15))
        return card_frame

    def setup_main_dashboard(self, parent):
        """Setup compact main dashboard with efficient space usage"""
        main_dashboard = ttk.Frame(parent)
        main_dashboard.grid(row=1, column=1, sticky=(tk.W, tk.E, tk.N, tk.S))
        main_dashboard.columnconfigure(0, weight=1)
        main_dashboard.rowconfigure(0, weight=0)  # Compact validation metrics
        main_dashboard.rowconfigure(1, weight=0)  # Compact insights panel
        main_dashboard.rowconfigure(2, weight=1)  # Expandable data preview

        # Top: Compact validation metrics (horizontal layout)
        self.setup_compact_validation_metrics(main_dashboard)

        # Middle: Compact insights panel (horizontal layout)
        self.setup_compact_insights_panel(main_dashboard)

        # Bottom: Data preview with tabs
        self.setup_preview_area(main_dashboard)

    def setup_compact_validation_metrics(self, parent):
        """Setup compact horizontal validation metrics"""
        metrics_frame = ttk.LabelFrame(parent, text="📊 Validation Metrics", padding="10")
        metrics_frame.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 10))

        # Horizontal layout for metrics
        metrics_frame.columnconfigure(0, weight=1)
        metrics_frame.columnconfigure(1, weight=1)
        metrics_frame.columnconfigure(2, weight=1)
        metrics_frame.columnconfigure(3, weight=1)
        metrics_frame.columnconfigure(4, weight=1)

        # Validation status (spans 2 columns)
        status_frame = ttk.Frame(metrics_frame)
        status_frame.grid(row=0, column=0, columnspan=2, sticky=(tk.W, tk.E), padx=(0, 10))

        self.validation_status_var = tk.StringVar()
        self.validation_status_var.set("⏳ Load data to begin validation")
        self.validation_status_label = ttk.Label(status_frame, textvariable=self.validation_status_var,
                                                font=('Segoe UI', 11, 'bold'))
        self.validation_status_label.pack(anchor=tk.W)

        # Compact metric cards (horizontal)
        self.metric_cards = {}
        metrics = [
            ('spend', '💰 Spend'),
            ('results', '🎯 Results'),
            ('reach', '👥 Reach'),
            ('impressions', '👁️ Impressions')
        ]

        for i, (key, title) in enumerate(metrics):
            card = self.create_compact_metric_card(metrics_frame, title, key)
            card.grid(row=0, column=i+1, sticky=(tk.W, tk.E), padx=5)
            self.metric_cards[key] = card

    def setup_compact_insights_panel(self, parent):
        """Setup compact horizontal insights panel"""
        insights_frame = ttk.LabelFrame(parent, text="💡 Ad Performance Insights", padding="10")
        insights_frame.grid(row=1, column=0, sticky=(tk.W, tk.E), pady=(0, 10))

        # Horizontal layout for insights
        insights_frame.columnconfigure(0, weight=1)
        insights_frame.columnconfigure(1, weight=1)
        insights_frame.columnconfigure(2, weight=1)
        insights_frame.columnconfigure(3, weight=1)

        # Overall metrics
        overall_frame = ttk.Frame(insights_frame)
        overall_frame.grid(row=0, column=0, sticky=(tk.W, tk.E), padx=(0, 10))

        ttk.Label(overall_frame, text="Overall CPR:", font=('Segoe UI', 9)).pack(anchor=tk.W)
        self.overall_cpr_var = tk.StringVar()
        self.overall_cpr_var.set("--")
        ttk.Label(overall_frame, textvariable=self.overall_cpr_var,
                 font=('Segoe UI', 12, 'bold'), foreground=self.colors['primary']).pack(anchor=tk.W)

        self.total_ads_var = tk.StringVar()
        self.total_ads_var.set("--")
        ttk.Label(overall_frame, textvariable=self.total_ads_var,
                 font=('Segoe UI', 9), foreground=self.colors['secondary']).pack(anchor=tk.W)

        # Top spender
        spender_frame = ttk.Frame(insights_frame)
        spender_frame.grid(row=0, column=1, sticky=(tk.W, tk.E), padx=5)

        ttk.Label(spender_frame, text="💰 Top Spender:", font=('Segoe UI', 9)).pack(anchor=tk.W)
        self.top_spender_var = tk.StringVar()
        self.top_spender_var.set("--")
        ttk.Label(spender_frame, textvariable=self.top_spender_var,
                 font=('Segoe UI', 10, 'bold')).pack(anchor=tk.W)

        # Most efficient
        efficient_frame = ttk.Frame(insights_frame)
        efficient_frame.grid(row=0, column=2, sticky=(tk.W, tk.E), padx=5)

        ttk.Label(efficient_frame, text="🎯 Most Efficient:", font=('Segoe UI', 9)).pack(anchor=tk.W)
        self.most_efficient_var = tk.StringVar()
        self.most_efficient_var.set("--")
        ttk.Label(efficient_frame, textvariable=self.most_efficient_var,
                 font=('Segoe UI', 10, 'bold')).pack(anchor=tk.W)

        # Categories summary
        categories_frame = ttk.Frame(insights_frame)
        categories_frame.grid(row=0, column=3, sticky=(tk.W, tk.E), padx=(10, 0))

        ttk.Label(categories_frame, text="🏷️ Categories:", font=('Segoe UI', 9)).pack(anchor=tk.W)
        self.categories_summary_var = tk.StringVar()
        self.categories_summary_var.set("--")
        ttk.Label(categories_frame, textvariable=self.categories_summary_var,
                 font=('Segoe UI', 9)).pack(anchor=tk.W)

    def create_compact_metric_card(self, parent, title, key):
        """Create a compact metric card for horizontal layout"""
        card = ttk.Frame(parent)

        # Title
        ttk.Label(card, text=title, font=('Segoe UI', 8),
                 foreground=self.colors['secondary']).pack(anchor=tk.W)

        # Meta value
        meta_value = ttk.Label(card, text="--", font=('Segoe UI', 10, 'bold'),
                              foreground=self.colors['primary'])
        meta_value.pack(anchor=tk.W)

        # Status
        status_label = ttk.Label(card, text="⏳", font=('Segoe UI', 8))
        status_label.pack(anchor=tk.W)

        # Store references
        card.meta_value = meta_value
        card.status_label = status_label

        return card

    def setup_validation_area(self, parent):
        """Setup modern validation area with dashboard-style cards"""
        validation_frame = ttk.Frame(parent)
        validation_frame.grid(row=1, column=1, sticky=(tk.W, tk.E, tk.N, tk.S))
        validation_frame.columnconfigure(0, weight=1)
        validation_frame.columnconfigure(1, weight=1)
        validation_frame.rowconfigure(1, weight=1)

        # Validation Status Header
        status_header = ttk.Frame(validation_frame)
        status_header.grid(row=0, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 20))
        status_header.columnconfigure(1, weight=1)

        status_title = ttk.Label(status_header, text="📊 Validation Dashboard",
                                style='Primary.TLabel', font=('Segoe UI', 16, 'bold'))
        status_title.grid(row=0, column=0, sticky=tk.W)

        self.validation_status_var = tk.StringVar()
        self.validation_status_var.set("⏳ Load data to begin validation")
        self.validation_status_label = ttk.Label(status_header, textvariable=self.validation_status_var,
                                                font=('Segoe UI', 12, 'bold'))
        self.validation_status_label.grid(row=0, column=1, sticky=tk.E)

        # Metrics Cards Row
        metrics_frame = ttk.Frame(validation_frame)
        metrics_frame.grid(row=1, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 20))
        metrics_frame.columnconfigure(0, weight=1)
        metrics_frame.columnconfigure(1, weight=1)
        metrics_frame.columnconfigure(2, weight=1)
        metrics_frame.columnconfigure(3, weight=1)

        # Create metric cards
        self.metric_cards = {}
        metrics = [
            ('spend', '💰 Spend', '$'),
            ('results', '🎯 Results', ''),
            ('reach', '👥 Reach', ''),
            ('impressions', '👁️ Impressions', '')
        ]

        for i, (key, title, prefix) in enumerate(metrics):
            card = self.create_metric_card(metrics_frame, title, key, prefix)
            card.grid(row=0, column=i, sticky=(tk.W, tk.E, tk.N, tk.S), padx=(0, 10 if i < 3 else 0))
            self.metric_cards[key] = card

        # Detailed Comparison Section
        comparison_frame = ttk.LabelFrame(validation_frame, text="📋 Detailed Comparison", padding="15")
        comparison_frame.grid(row=2, column=0, columnspan=2, sticky=(tk.W, tk.E, tk.N, tk.S))
        comparison_frame.columnconfigure(0, weight=1)
        comparison_frame.columnconfigure(1, weight=1)
        comparison_frame.rowconfigure(0, weight=1)

        # Meta's Official Data
        meta_card = ttk.LabelFrame(comparison_frame, text="Meta's Official Summary", padding="10")
        meta_card.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), padx=(0, 10))

        self.meta_summary_display = self.create_modern_text_display(meta_card, '#e8f4fd')

        # Our Aggregated Data
        our_card = ttk.LabelFrame(comparison_frame, text="Our Aggregated Totals", padding="10")
        our_card.grid(row=0, column=1, sticky=(tk.W, tk.E, tk.N, tk.S))

        self.our_totals_display = self.create_modern_text_display(our_card, '#f0f8e8')

    def create_metric_card(self, parent, title, key, prefix):
        """Create a modern metric card widget"""
        card = ttk.LabelFrame(parent, text=title, padding="15")

        # Meta value
        meta_label = ttk.Label(card, text="Meta Official", font=('Segoe UI', 9),
                              foreground=self.colors['secondary'])
        meta_label.pack(anchor=tk.W)

        meta_value = ttk.Label(card, text="--", font=('Segoe UI', 14, 'bold'),
                              foreground=self.colors['primary'])
        meta_value.pack(anchor=tk.W)

        # Our value
        our_label = ttk.Label(card, text="Our Calculation", font=('Segoe UI', 9),
                             foreground=self.colors['secondary'])
        our_label.pack(anchor=tk.W, pady=(10, 0))

        our_value = ttk.Label(card, text="--", font=('Segoe UI', 14, 'bold'))
        our_value.pack(anchor=tk.W)

        # Status indicator
        status_label = ttk.Label(card, text="⏳ Pending", font=('Segoe UI', 10, 'bold'))
        status_label.pack(anchor=tk.W, pady=(10, 0))

        # Store references for updates
        card.meta_value = meta_value
        card.our_value = our_value
        card.status_label = status_label
        card.prefix = prefix

        return card

    def create_modern_text_display(self, parent, bg_color):
        """Create a modern text display widget"""
        text_frame = ttk.Frame(parent)
        text_frame.pack(fill=tk.BOTH, expand=True)
        text_frame.columnconfigure(0, weight=1)
        text_frame.rowconfigure(0, weight=1)

        text_widget = tk.Text(text_frame, font=('Segoe UI', 10), state=tk.DISABLED,
                             bg=bg_color, wrap=tk.WORD, relief=tk.FLAT,
                             borderwidth=0, padx=10, pady=10)
        text_widget.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        scrollbar = ttk.Scrollbar(text_frame, orient=tk.VERTICAL, command=text_widget.yview)
        text_widget.configure(yscrollcommand=scrollbar.set)
        scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))

        return text_widget
    
    def setup_ad_insights_panel(self, parent):
        """Setup comprehensive Ad Insights visualization panel"""
        insights_panel = ttk.LabelFrame(parent, text="💡 Ad Performance Insights Dashboard", padding="15")
        insights_panel.grid(row=2, column=0, columnspan=2, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(20, 0))
        insights_panel.columnconfigure(0, weight=1)
        insights_panel.columnconfigure(1, weight=1)
        insights_panel.columnconfigure(2, weight=1)
        insights_panel.rowconfigure(0, weight=1)
        insights_panel.rowconfigure(1, weight=1)

        # Top row - Key metrics cards
        self.setup_insights_metrics_row(insights_panel)

        # Bottom row - Performance breakdown and top performers
        self.setup_insights_details_row(insights_panel)

    def setup_insights_metrics_row(self, parent):
        """Setup top row with key metrics cards"""
        # Overall Performance Card
        overall_card = ttk.LabelFrame(parent, text="📊 Overall Performance", padding="10")
        overall_card.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), padx=(0, 10))

        self.overall_cpr_var = tk.StringVar()
        self.overall_cpr_var.set("--")
        overall_cpr_label = ttk.Label(overall_card, text="Overall Cost per Result:",
                                     font=('Segoe UI', 10))
        overall_cpr_label.pack(anchor=tk.W)
        overall_cpr_value = ttk.Label(overall_card, textvariable=self.overall_cpr_var,
                                     font=('Segoe UI', 18, 'bold'), foreground=self.colors['primary'])
        overall_cpr_value.pack(anchor=tk.W)

        self.total_ads_var = tk.StringVar()
        self.total_ads_var.set("--")
        total_ads_label = ttk.Label(overall_card, text="Total Ads Analyzed:",
                                   font=('Segoe UI', 10))
        total_ads_label.pack(anchor=tk.W, pady=(10, 0))
        total_ads_value = ttk.Label(overall_card, textvariable=self.total_ads_var,
                                   font=('Segoe UI', 16, 'bold'), foreground=self.colors['secondary'])
        total_ads_value.pack(anchor=tk.W)

        # Performance Categories Card
        categories_card = ttk.LabelFrame(parent, text="🏷️ Performance Categories", padding="10")
        categories_card.grid(row=0, column=1, sticky=(tk.W, tk.E, tk.N, tk.S), padx=(5, 5))

        self.categories_display = tk.Text(categories_card, height=6, font=('Segoe UI', 9),
                                         state=tk.DISABLED, bg='#f8fafc', wrap=tk.WORD,
                                         relief=tk.FLAT, borderwidth=0)
        self.categories_display.pack(fill=tk.BOTH, expand=True)

        # Ad Validation Status Card
        validation_card = ttk.LabelFrame(parent, text="✅ Ad-Level Validation", padding="10")
        validation_card.grid(row=0, column=2, sticky=(tk.W, tk.E, tk.N, tk.S), padx=(10, 0))

        self.ad_validation_status_var = tk.StringVar()
        self.ad_validation_status_var.set("⏳ Pending")
        ad_validation_label = ttk.Label(validation_card, textvariable=self.ad_validation_status_var,
                                       font=('Segoe UI', 14, 'bold'))
        ad_validation_label.pack(anchor=tk.W)

        self.ad_validation_details = tk.Text(validation_card, height=4, font=('Segoe UI', 9),
                                            state=tk.DISABLED, bg='#f0f9ff', wrap=tk.WORD,
                                            relief=tk.FLAT, borderwidth=0)
        self.ad_validation_details.pack(fill=tk.BOTH, expand=True, pady=(10, 0))

    def setup_insights_details_row(self, parent):
        """Setup bottom row with detailed insights"""
        # Top Spenders Card
        spenders_card = ttk.LabelFrame(parent, text="💰 Top 5 Spenders", padding="10")
        spenders_card.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), padx=(0, 10), pady=(10, 0))

        self.top_spenders_display = tk.Text(spenders_card, height=6, font=('Segoe UI', 9),
                                           state=tk.DISABLED, bg='#fef3c7', wrap=tk.WORD,
                                           relief=tk.FLAT, borderwidth=0)
        self.top_spenders_display.pack(fill=tk.BOTH, expand=True)

        # Most Efficient Ads Card
        efficient_card = ttk.LabelFrame(parent, text="🎯 Most Efficient Ads", padding="10")
        efficient_card.grid(row=1, column=1, sticky=(tk.W, tk.E, tk.N, tk.S), padx=(5, 5), pady=(10, 0))

        self.efficient_ads_display = tk.Text(efficient_card, height=6, font=('Segoe UI', 9),
                                            state=tk.DISABLED, bg='#d1fae5', wrap=tk.WORD,
                                            relief=tk.FLAT, borderwidth=0)
        self.efficient_ads_display.pack(fill=tk.BOTH, expand=True)

        # Performance Insights Card
        insights_card = ttk.LabelFrame(parent, text="📈 Key Insights", padding="10")
        insights_card.grid(row=1, column=2, sticky=(tk.W, tk.E, tk.N, tk.S), padx=(10, 0), pady=(10, 0))

        self.key_insights_display = tk.Text(insights_card, height=6, font=('Segoe UI', 9),
                                           state=tk.DISABLED, bg='#e0e7ff', wrap=tk.WORD,
                                           relief=tk.FLAT, borderwidth=0)
        self.key_insights_display.pack(fill=tk.BOTH, expand=True)

    def setup_preview_area(self, parent):
        """Setup compact preview area with tabbed interface"""
        preview_frame = ttk.LabelFrame(parent, text="📋 Data Preview & Analysis", padding="10")
        preview_frame.grid(row=2, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        preview_frame.columnconfigure(0, weight=1)
        preview_frame.rowconfigure(0, weight=1)

        # Modern notebook with enhanced styling
        self.notebook = ttk.Notebook(preview_frame)
        self.notebook.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        # Setup all tabs with modern design
        self.setup_ad_performance_tab()  # NEW: Ad Performance first
        self.setup_ad_insights_tab()     # NEW: Ad Insights second
        self.setup_airtable_tab()
        self.setup_summary_detail_tab()
        self.setup_monthly_tab()
        self.setup_daily_tab()
        self.setup_cleaned_tab()
        self.setup_raw_tab()
        self.setup_quality_tab()

    def setup_ad_performance_tab(self):
        """Setup Ad Performance Analysis tab"""
        ad_perf_frame = ttk.Frame(self.notebook)
        self.notebook.add(ad_perf_frame, text="🏆 Ad Performance")

        # Ad performance table with all KPIs
        columns = ('ad_name', 'campaign_name', 'spend', 'results', 'reach', 'impressions',
                  'frequency', 'cost_per_result', 'status', 'start_date', 'end_date',
                  'days_active', 'category')
        self.ad_performance_tree = ttk.Treeview(ad_perf_frame, columns=columns, show='headings', height=15)

        # Configure columns with appropriate widths
        column_widths = {
            'ad_name': 200, 'campaign_name': 150, 'spend': 80, 'results': 70,
            'reach': 80, 'impressions': 90, 'frequency': 70, 'cost_per_result': 90,
            'status': 80, 'start_date': 80, 'end_date': 80, 'days_active': 80, 'category': 120
        }

        for col in columns:
            self.ad_performance_tree.heading(col, text=col.replace('_', ' ').title())
            self.ad_performance_tree.column(col, width=column_widths.get(col, 100), anchor=tk.CENTER)

        # Scrollbars
        ad_perf_scroll_y = ttk.Scrollbar(ad_perf_frame, orient=tk.VERTICAL,
                                        command=self.ad_performance_tree.yview)
        ad_perf_scroll_x = ttk.Scrollbar(ad_perf_frame, orient=tk.HORIZONTAL,
                                        command=self.ad_performance_tree.xview)
        self.ad_performance_tree.configure(yscrollcommand=ad_perf_scroll_y.set,
                                          xscrollcommand=ad_perf_scroll_x.set)

        self.ad_performance_tree.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        ad_perf_scroll_y.grid(row=0, column=1, sticky=(tk.N, tk.S))
        ad_perf_scroll_x.grid(row=1, column=0, sticky=(tk.W, tk.E))

        ad_perf_frame.columnconfigure(0, weight=1)
        ad_perf_frame.rowconfigure(0, weight=1)

    def setup_ad_insights_tab(self):
        """Setup Ad Insights tab"""
        insights_frame = ttk.Frame(self.notebook)
        self.notebook.add(insights_frame, text="💡 Ad Insights")

        # Create insights display
        self.ad_insights_display = self.create_modern_text_display(insights_frame, '#f0f9ff')

    def setup_quality_tab(self):
        """Setup modern quality report tab"""
        quality_frame = ttk.Frame(self.notebook)
        self.notebook.add(quality_frame, text="📊 Quality Report")

        # Quality metrics display with modern styling
        self.quality_text = self.create_modern_text_display(quality_frame, '#f8fafc')

    def setup_summary_detail_tab(self):
        # NEW: Detailed summary row view
        summary_detail_frame = ttk.Frame(self.notebook)
        self.notebook.add(summary_detail_frame, text="📋 Summary Row Details")
        
        # Summary row detailed view
        columns = ('Field', 'Meta Official Value', 'Data Type', 'Notes')
        self.summary_detail_tree = ttk.Treeview(summary_detail_frame, columns=columns, show='headings', height=15)
        
        # Configure columns
        column_widths = {'Field': 200, 'Meta Official Value': 150, 'Data Type': 100, 'Notes': 300}
        
        for col in columns:
            self.summary_detail_tree.heading(col, text=col)
            self.summary_detail_tree.column(col, width=column_widths.get(col, 150), anchor=tk.W)
        
        # Scrollbars
        summary_detail_scroll_y = ttk.Scrollbar(summary_detail_frame, orient=tk.VERTICAL, 
                                              command=self.summary_detail_tree.yview)
        summary_detail_scroll_x = ttk.Scrollbar(summary_detail_frame, orient=tk.HORIZONTAL, 
                                              command=self.summary_detail_tree.xview)
        self.summary_detail_tree.configure(yscrollcommand=summary_detail_scroll_y.set, 
                                         xscrollcommand=summary_detail_scroll_x.set)
        
        self.summary_detail_tree.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        summary_detail_scroll_y.grid(row=0, column=1, sticky=(tk.N, tk.S))
        summary_detail_scroll_x.grid(row=1, column=0, sticky=(tk.W, tk.E))
        
        summary_detail_frame.columnconfigure(0, weight=1)
        summary_detail_frame.rowconfigure(0, weight=1)
    
    def setup_airtable_tab(self):
        airtable_frame = ttk.Frame(self.notebook)
        self.notebook.add(airtable_frame, text="Airtable Export Preview")
        
        # Airtable-optimized data preview
        columns = ('unique_id', 'date', 'month', 'ad_name', 'campaign_name', 'spend_usd', 
                  'results', 'reach', 'impressions', 'frequency', 'status')
        self.airtable_tree = ttk.Treeview(airtable_frame, columns=columns, show='headings', height=12)
        
        # Configure columns with appropriate widths
        column_widths = {'unique_id': 100, 'date': 80, 'month': 70, 'ad_name': 150, 
                        'campaign_name': 150, 'spend_usd': 80, 'results': 70, 'reach': 80, 
                        'impressions': 90, 'frequency': 70, 'status': 80}
        
        for col in columns:
            self.airtable_tree.heading(col, text=col.replace('_', ' ').title())
            self.airtable_tree.column(col, width=column_widths.get(col, 100), anchor=tk.CENTER)
        
        # Scrollbars
        airtable_scroll_y = ttk.Scrollbar(airtable_frame, orient=tk.VERTICAL, 
                                        command=self.airtable_tree.yview)
        airtable_scroll_x = ttk.Scrollbar(airtable_frame, orient=tk.HORIZONTAL, 
                                        command=self.airtable_tree.xview)
        self.airtable_tree.configure(yscrollcommand=airtable_scroll_y.set, 
                                   xscrollcommand=airtable_scroll_x.set)
        
        self.airtable_tree.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        airtable_scroll_y.grid(row=0, column=1, sticky=(tk.N, tk.S))
        airtable_scroll_x.grid(row=1, column=0, sticky=(tk.W, tk.E))
        
        airtable_frame.columnconfigure(0, weight=1)
        airtable_frame.rowconfigure(0, weight=1)
    
    def setup_monthly_tab(self):
        monthly_frame = ttk.Frame(self.notebook)
        self.notebook.add(monthly_frame, text="Monthly Summary")
        
        columns = ('Month', 'Amount Spent (USD)', 'Results', 'Entry Count', 'Avg Frequency', 'Data Quality')
        self.monthly_tree = ttk.Treeview(monthly_frame, columns=columns, show='headings', height=12)
        
        for col in columns:
            self.monthly_tree.heading(col, text=col)
            self.monthly_tree.column(col, width=120, anchor=tk.CENTER)
        
        monthly_scroll_y = ttk.Scrollbar(monthly_frame, orient=tk.VERTICAL, 
                                       command=self.monthly_tree.yview)
        monthly_scroll_x = ttk.Scrollbar(monthly_frame, orient=tk.HORIZONTAL, 
                                       command=self.monthly_tree.xview)
        self.monthly_tree.configure(yscrollcommand=monthly_scroll_y.set, 
                                  xscrollcommand=monthly_scroll_x.set)
        
        self.monthly_tree.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        monthly_scroll_y.grid(row=0, column=1, sticky=(tk.N, tk.S))
        monthly_scroll_x.grid(row=1, column=0, sticky=(tk.W, tk.E))
        
        monthly_frame.columnconfigure(0, weight=1)
        monthly_frame.rowconfigure(0, weight=1)
    
    def setup_daily_tab(self):
        daily_frame = ttk.Frame(self.notebook)
        self.notebook.add(daily_frame, text="Daily Aggregated")
        
        columns = ('Date', 'Total Spend', 'Total Results', 'Total Reach', 'Total Impressions', 'Frequency')
        self.daily_tree = ttk.Treeview(daily_frame, columns=columns, show='headings', height=12)
        
        for col in columns:
            self.daily_tree.heading(col, text=col)
            self.daily_tree.column(col, width=100, anchor=tk.CENTER)
        
        daily_scroll_y = ttk.Scrollbar(daily_frame, orient=tk.VERTICAL, 
                                     command=self.daily_tree.yview)
        daily_scroll_x = ttk.Scrollbar(daily_frame, orient=tk.HORIZONTAL, 
                                     command=self.daily_tree.xview)
        self.daily_tree.configure(yscrollcommand=daily_scroll_y.set, 
                                xscrollcommand=daily_scroll_x.set)
        
        self.daily_tree.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        daily_scroll_y.grid(row=0, column=1, sticky=(tk.N, tk.S))
        daily_scroll_x.grid(row=1, column=0, sticky=(tk.W, tk.E))
        
        daily_frame.columnconfigure(0, weight=1)
        daily_frame.rowconfigure(0, weight=1)
    
    def setup_cleaned_tab(self):
        cleaned_frame = ttk.Frame(self.notebook)
        self.notebook.add(cleaned_frame, text="Cleaned Data")
        
        columns = ('Date', 'Ad Name', 'Campaign', 'Amount Spent', 'Results', 'Reach', 'Impressions', 'Status')
        self.cleaned_tree = ttk.Treeview(cleaned_frame, columns=columns, show='headings', height=12)
        
        for col in columns:
            self.cleaned_tree.heading(col, text=col)
            self.cleaned_tree.column(col, width=120, anchor=tk.CENTER)
        
        cleaned_scroll_y = ttk.Scrollbar(cleaned_frame, orient=tk.VERTICAL, 
                                       command=self.cleaned_tree.yview)
        cleaned_scroll_x = ttk.Scrollbar(cleaned_frame, orient=tk.HORIZONTAL, 
                                       command=self.cleaned_tree.xview)
        self.cleaned_tree.configure(yscrollcommand=cleaned_scroll_y.set, 
                                  xscrollcommand=cleaned_scroll_x.set)
        
        self.cleaned_tree.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        cleaned_scroll_y.grid(row=0, column=1, sticky=(tk.N, tk.S))
        cleaned_scroll_x.grid(row=1, column=0, sticky=(tk.W, tk.E))
        
        cleaned_frame.columnconfigure(0, weight=1)
        cleaned_frame.rowconfigure(0, weight=1)
    
    def setup_raw_tab(self):
        raw_frame = ttk.Frame(self.notebook)
        self.notebook.add(raw_frame, text="Raw Data Sample")
        
        columns = ('Date', 'Ad Name', 'Amount Spent', 'Results', 'Reach', 'Impressions', 'Issues')
        self.raw_tree = ttk.Treeview(raw_frame, columns=columns, show='headings', height=12)
        
        for col in columns:
            self.raw_tree.heading(col, text=col)
            self.raw_tree.column(col, width=120, anchor=tk.CENTER)
        
        raw_scroll_y = ttk.Scrollbar(raw_frame, orient=tk.VERTICAL, 
                                   command=self.raw_tree.yview)
        raw_scroll_x = ttk.Scrollbar(raw_frame, orient=tk.HORIZONTAL, 
                                   command=self.raw_tree.xview)
        self.raw_tree.configure(yscrollcommand=raw_scroll_y.set, 
                              xscrollcommand=raw_scroll_x.set)
        
        self.raw_tree.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        raw_scroll_y.grid(row=0, column=1, sticky=(tk.N, tk.S))
        raw_scroll_x.grid(row=1, column=0, sticky=(tk.W, tk.E))
        
        raw_frame.columnconfigure(0, weight=1)
        raw_frame.rowconfigure(0, weight=1)
    
    def load_csv(self):
        try:
            file_path = filedialog.askopenfilename(
                title="Select Meta Ads CSV File",
                filetypes=[("CSV files", "*.csv"), ("All files", "*.*")]
            )
            
            if not file_path:
                return
            
            self.update_progress(10, "Loading CSV file...")
            
            # Load and separate summary from granular data
            self.raw_data, self.summary_row = self.load_and_separate_data(file_path)
            
            if self.raw_data is not None and self.summary_row is not None:
                self.update_progress(30, "Data loaded and separated successfully")
                self.update_file_info_display()
                self.update_raw_data_display()
                self.update_summary_displays()
                self.generate_quality_report()
                self.status_var.set(f"✅ Loaded {len(self.raw_data)} granular records + 1 summary row")
            else:
                self.update_progress(0, "Failed to load or separate CSV data")
            
        except Exception as e:
            self.handle_error("Error loading CSV", e)
            self.update_progress(0, "Error loading file")
    
    def load_and_separate_data(self, file_path):
        """Load CSV and separate summary row from granular data"""
        try:
            # Load CSV with robust encoding detection
            try:
                df = pd.read_csv(file_path, encoding='utf-8')
            except UnicodeDecodeError:
                df = pd.read_csv(file_path, encoding='latin-1')
            
            self.update_progress(15, "Identifying summary and granular rows...")
            
            # Identify summary row (usually first row with missing Ad name or specific patterns)
            summary_candidates = df[
                (df['Ad name'].isna()) | 
                (df['Ad name'].astype(str).str.lower().isin(['nan', 'none', ''])) |
                (df['Ad Set Name'].isna()) |
                (df['Ad Set Name'].astype(str).str.lower().isin(['nan', 'none', '']))
            ]
            
            if len(summary_candidates) == 0:
                # If no clear summary row, check if first row has aggregated characteristics
                first_row = df.iloc[0]
                if (pd.isna(first_row.get('Ad name')) or 
                    str(first_row.get('Ad name')).lower() in ['nan', 'none', '']):
                    summary_row = first_row
                    granular_data = df.iloc[1:].copy()
                else:
                    messagebox.showwarning("No Summary Row", 
                                         "Could not identify summary row. Using all data as granular.")
                    summary_row = None
                    granular_data = df.copy()
            else:
                # Use the first summary candidate (usually the actual summary)
                summary_row = summary_candidates.iloc[0]
                # Remove all summary candidates from granular data
                granular_data = df.drop(summary_candidates.index).copy()
            
            self.update_progress(20, "Validating data structure...")
            
            # Validate required columns in granular data
            required_columns = ['Reporting ends', 'Amount spent (USD)', 'Reach', 'Impressions']
            missing_columns = [col for col in required_columns if col not in granular_data.columns]
            
            if missing_columns:
                messagebox.showerror("Missing Columns", 
                                   f"Required columns missing: {', '.join(missing_columns)}")
                return None, None
            
            # Store file info
            self.data_quality_report = {
                'source_file': os.path.basename(file_path),
                'original_rows': len(df),
                'summary_rows': 1 if summary_row is not None else 0,
                'granular_rows': len(granular_data),
                'load_timestamp': datetime.now()
            }
            
            return granular_data, summary_row
            
        except Exception as e:
            messagebox.showerror("File Error", f"Error reading CSV file: {str(e)}")
            return None, None
    
    def process_data(self):
        if self.raw_data is None:
            messagebox.showwarning("No Data", "Please load a CSV file first")
            return
        
        try:
            self.processing_timestamp = datetime.now()
            self.update_progress(40, "Processing granular data...")
            
            # Clean granular data (never touch summary row)
            if self.clean_data_var.get():
                self.granular_data = self.clean_granular_data(self.raw_data.copy())
                self.cleaned_data = self.granular_data
                self.update_cleaned_display()
            else:
                self.granular_data = self.raw_data.copy()
            
            self.update_progress(60, "Generating aggregations...")
            
            # Generate aggregations from granular data only
            if self.monthly_var.get():
                self.monthly_summary = self.aggregate_by_period(self.granular_data, 'M')
                self.update_monthly_display()
            
            if self.daily_var.get():
                self.daily_aggregated = self.aggregate_by_period(self.granular_data, 'D')
                self.update_daily_display()
            
            self.update_progress(80, "Preparing Airtable export...")
            
            # Generate Airtable-optimized export
            if self.airtable_var.get():
                self.airtable_export = self.prepare_airtable_export()
                self.update_airtable_display()
            
            self.update_progress(90, "Validating against summary row...")
            
            # CRITICAL: Validate our aggregations against Meta's summary
            if self.validate_summary_var.get() and self.summary_row is not None:
                self.perform_summary_validation()

            # NEW: Run Ad Performance Analysis
            self.update_progress(85, "🔍 Running Ad Performance Analysis...")
            self.status_var.set("🔍 Analyzing ad-level performance and generating insights...")
            ad_table, ad_status = self.analyze_ads()
            if ad_table is not None:
                self.update_progress(90, "📊 Updating ad performance displays...")
                self.update_ad_performance_display()
                self.update_progress(95, "💡 Generating visual insights...")

            self.update_progress(100, "Processing, validation, and ad analysis completed successfully")
            self.status_var.set("✅ Data processing, validation, and ad analysis completed successfully")
            
        except Exception as e:
            self.handle_error("Error processing data", e)
            self.update_progress(0, "Error processing data")
    
    def clean_granular_data(self, df):
        """Clean only granular data, never touch summary row"""
        try:
            cleaned_df = df.copy()
            
            # Standardize column names for Airtable compatibility
            column_mapping = {
                'Reporting ends': 'reporting_date',
                'Reporting starts': 'reporting_start_date',
                'Ad name': 'ad_name',
                'Ad Set Name': 'campaign_name',
                'Amount spent (USD)': 'amount_spent_usd',
                'Results': 'results',
                'Reach': 'reach',
                'Impressions': 'impressions',
                'Ad delivery': 'ad_status',
                'Cost per results': 'cost_per_result'
            }
            
            # Rename columns
            for old_name, new_name in column_mapping.items():
                if old_name in cleaned_df.columns:
                    cleaned_df = cleaned_df.rename(columns={old_name: new_name})
            
            # Clean and validate dates
            if 'reporting_date' in cleaned_df.columns:
                cleaned_df['reporting_date'] = pd.to_datetime(cleaned_df['reporting_date'], errors='coerce')
                invalid_dates = cleaned_df['reporting_date'].isnull()
                if invalid_dates.any():
                    for idx in cleaned_df[invalid_dates].index:
                        self.validation_errors.append({
                            'row': idx, 'field': 'reporting_date', 'error_type': 'Invalid Date',
                            'original_value': str(df.iloc[idx].get('Reporting ends', 'N/A')),
                            'corrected_value': 'Excluded', 'action_taken': 'Row removed'
                        })
                cleaned_df = cleaned_df[~invalid_dates]
            
            # Clean and validate numeric columns
            numeric_columns = ['amount_spent_usd', 'results', 'reach', 'impressions', 'cost_per_result']
            for col in numeric_columns:
                if col in cleaned_df.columns:
                    original_values = cleaned_df[col].copy()
                    cleaned_df[col] = pd.to_numeric(cleaned_df[col].astype(str).str.replace(',', ''), errors='coerce')
                    
                    # Fill NaN with 0 for spend and metrics
                    if col in ['amount_spent_usd', 'results', 'reach', 'impressions']:
                        cleaned_df[col] = cleaned_df[col].fillna(0)
            
            # Clean text fields
            text_columns = ['ad_name', 'campaign_name', 'ad_status']
            for col in text_columns:
                if col in cleaned_df.columns:
                    cleaned_df[col] = cleaned_df[col].astype(str).str.strip()
                    cleaned_df[col] = cleaned_df[col].replace(['nan', 'NaN', 'None', ''], 'Unknown')
            
            # Calculate derived metrics
            if 'reach' in cleaned_df.columns and 'impressions' in cleaned_df.columns:
                cleaned_df['frequency'] = np.where(
                    cleaned_df['reach'] > 0,
                    cleaned_df['impressions'] / cleaned_df['reach'],
                    0
                )
            
            # Add month column for aggregation
            if 'reporting_date' in cleaned_df.columns:
                cleaned_df['month'] = cleaned_df['reporting_date'].dt.to_period('M').astype(str)
            
            return cleaned_df
            
        except Exception as e:
            raise Exception(f"Error in granular data cleaning: {str(e)}")
    
    def aggregate_by_period(self, df, period):
        """Aggregate granular data by period"""
        try:
            if 'reporting_date' not in df.columns:
                raise Exception("Missing reporting_date column for aggregation")
            
            # Group by period
            grouped = df.groupby(df['reporting_date'].dt.to_period(period)).agg({
                'amount_spent_usd': 'sum',
                'results': 'sum',
                'reach': 'sum',
                'impressions': 'sum',
                'ad_name': 'count',  # Count entries
                'frequency': 'mean'  # Average frequency
            }).reset_index()
            
            # Rename columns
            grouped = grouped.rename(columns={
                'reporting_date': 'period',
                'amount_spent_usd': 'total_spend',
                'results': 'total_results',
                'reach': 'total_reach',
                'impressions': 'total_impressions',
                'ad_name': 'entry_count',
                'frequency': 'avg_frequency'
            })
            
            # Recalculate frequency for aggregated data
            grouped['frequency'] = np.where(
                grouped['total_reach'] > 0,
                grouped['total_impressions'] / grouped['total_reach'],
                0
            )
            
            # Format period
            grouped['period'] = grouped['period'].astype(str)
            
            # Add data quality score
            grouped['data_quality_score'] = np.where(
                (grouped['total_spend'] > 0) & (grouped['total_results'] > 0),
                'Good', 'Incomplete'
            )
            
            return grouped
            
        except Exception as e:
            raise Exception(f"Error in aggregation: {str(e)}")
    
    def perform_summary_validation(self):
        """CRITICAL: Compare our aggregated totals with Meta's official summary"""
        try:
            if self.summary_row is None or self.granular_data is None:
                return

            # Extract Meta's official totals from summary row
            meta_totals = {
                'spend': self.safe_numeric_extract(self.summary_row, 'Amount spent (USD)'),
                'results': self.safe_numeric_extract(self.summary_row, 'Results'),
                'reach': self.safe_numeric_extract(self.summary_row, 'Reach'),
                'impressions': self.safe_numeric_extract(self.summary_row, 'Impressions'),
                'date_start': self.summary_row.get('Reporting starts', 'N/A'),
                'date_end': self.summary_row.get('Reporting ends', 'N/A')
            }

            # Calculate our aggregated totals from granular data
            our_totals = {
                'spend': self.granular_data['amount_spent_usd'].sum(),
                'results': self.granular_data['results'].sum(),
                'reach': self.granular_data['reach'].sum(),
                'impressions': self.granular_data['impressions'].sum(),
                'records': len(self.granular_data)
            }

            # Perform validation checks with special handling for reach (deduplicated)
            validation_results = {}
            tolerance = 0.01  # 1 cent tolerance for spend, 1 unit for others

            for metric in ['spend', 'results', 'reach', 'impressions']:
                meta_val = meta_totals[metric]
                our_val = our_totals[metric]

                # Special handling for reach - Meta uses deduplicated reach
                if metric == 'reach':
                    if pd.isna(meta_val) or meta_val == 0:
                        if our_val == 0:
                            validation_results[metric] = {'status': 'PASS', 'note': 'Both zero - OK'}
                        else:
                            validation_results[metric] = {'status': 'WARNING', 'note': 'Meta=0 but we have data'}
                    else:
                        # For reach, our aggregated value will always be higher due to deduplication
                        # Meta's reach is deduplicated (removes same people seeing ads multiple times)
                        # Our reach is sum of all individual ad reaches (includes duplicates)
                        if our_val >= meta_val:
                            validation_results[metric] = {'status': 'PASS', 'note': 'Expected: Our reach > Meta (deduplicated)'}
                        else:
                            validation_results[metric] = {'status': 'WARNING', 'note': 'Unexpected: Our reach < Meta reach'}

                # Special handling for results - sometimes Meta doesn't track results in summary
                elif metric == 'results':
                    if pd.isna(meta_val) or meta_val == 0:
                        if our_val == 0:
                            validation_results[metric] = {'status': 'PASS', 'note': 'Both zero - OK'}
                        else:
                            validation_results[metric] = {'status': 'PASS', 'note': 'Meta=0, our data available (normal)'}
                    else:
                        diff = abs(meta_val - our_val)
                        pct_diff = (diff / meta_val) * 100 if meta_val != 0 else 0

                        if diff <= tolerance:
                            validation_results[metric] = {'status': 'PASS', 'note': 'Perfect match'}
                        elif pct_diff <= 1:
                            validation_results[metric] = {'status': 'PASS', 'note': f'Close match ({pct_diff:.2f}% diff)'}
                        elif pct_diff <= 5:
                            validation_results[metric] = {'status': 'WARNING', 'note': f'Acceptable diff ({pct_diff:.2f}%)'}
                        else:
                            validation_results[metric] = {'status': 'FAIL', 'note': f'Large discrepancy ({pct_diff:.2f}%)'}

                # Standard validation for spend and impressions
                else:
                    if pd.isna(meta_val) or meta_val == 0:
                        if our_val == 0:
                            validation_results[metric] = {'status': 'PASS', 'note': 'Both zero - OK'}
                        else:
                            validation_results[metric] = {'status': 'WARNING', 'note': 'Meta=0 but we have data'}
                    else:
                        diff = abs(meta_val - our_val)
                        pct_diff = (diff / meta_val) * 100 if meta_val != 0 else 0

                        if diff <= tolerance:
                            validation_results[metric] = {'status': 'PASS', 'note': 'Perfect match'}
                        elif pct_diff <= 0.01:  # 0.01% tolerance for spend/impressions
                            validation_results[metric] = {'status': 'PASS', 'note': f'Close match ({pct_diff:.3f}% diff)'}
                        elif pct_diff <= 1:
                            validation_results[metric] = {'status': 'WARNING', 'note': f'Minor diff ({pct_diff:.2f}%)'}
                        else:
                            validation_results[metric] = {'status': 'FAIL', 'note': f'Large discrepancy ({pct_diff:.2f}%)'}

                validation_results[metric]['meta_value'] = meta_val
                validation_results[metric]['our_value'] = our_val
                validation_results[metric]['difference'] = our_val - meta_val
            
            # Store results
            self.accuracy_check = {
                'meta_totals': meta_totals,
                'our_totals': our_totals,
                'validation_results': validation_results,
                'overall_status': self.determine_overall_status(validation_results),
                'validation_timestamp': datetime.now()
            }
            
            # Update displays
            self.update_validation_displays()
            
        except Exception as e:
            self.handle_error("Error in summary validation", e)

    # ============================================================================
    # AD PERFORMANCE ANALYZER - Following exact specifications
    # ============================================================================

    def load_meta_csv_for_ads(self, df, summary_row):
        """Load and separate data for ad analysis - using existing loaded data"""
        # We already have the data separated, just return it
        granular = df.copy()
        return granular, summary_row

    def clean_granular_for_ads(self, df):
        """Clean granular rows for ad analysis - exact specification"""
        # Drop rows without ad name
        df = df[df['Ad name'].notna() & (df['Ad name'] != '')].copy()

        # Convert dates
        df['Reporting ends'] = pd.to_datetime(df['Reporting ends'], errors='coerce')
        df = df[df['Reporting ends'].notna()]

        # Numeric coercion - exact specification
        num_cols = ['Amount spent (USD)', 'Results', 'Reach', 'Impressions', 'Cost per results']
        for c in num_cols:
            df[c] = (df[c].astype(str)
                           .str.replace(',', '', regex=False)
                           .replace(['nan', ''], 0)
                           .astype(float)
                           .fillna(0))

        # Rename for convenience - exact specification
        df = df.rename(columns={
            'Reporting ends': 'date',
            'Ad name': 'ad_name',
            'Ad Set Name': 'campaign_name',
            'Amount spent (USD)': 'spend',
            'Cost per results': 'avg_cost_result',
            'Ad delivery': 'status'
        })

        # Derived - exact specification
        df['frequency'] = np.where(df['Reach'] > 0,
                                   df['Impressions'] / df['Reach'], 0)
        return df

    def build_ad_table(self, df):
        """Aggregate to ad-level table - exact specification"""
        grp = (df.groupby(['ad_name', 'campaign_name'])
                 .agg(spend=('spend', 'sum'),
                      results=('Results', 'sum'),
                      reach=('Reach', 'sum'),
                      impressions=('Impressions', 'sum'),
                      status=('status', 'last'),
                      start_date=('date', 'min'),
                      end_date=('date', 'max'),
                      days_active=('date', 'nunique'))
                 .reset_index())

        grp['frequency'] = np.where(grp['reach'] > 0,
                                    grp['impressions'] / grp['reach'], 0)
        grp['cost_per_result'] = np.where(grp['results'] > 0,
                                          grp['spend'] / grp['results'],
                                          np.nan)
        return grp.sort_values('spend', ascending=False)

    def tag_category(self, row, overall_cpr):
        """Performance categorization - exact specification"""
        if row.results == 0:
            return '❌ No Results'
        if row.cost_per_result > 10:
            return '💸 High Cost'
        if row.frequency > 3:
            return '🔄 Ad Fatigue Risk'
        if row.spend < 50:
            return '📊 Low Spend'
        if row.spend >= 500 and row.cost_per_result <= 1:
            return '🏆 Top Performer'
        if row.cost_per_result <= overall_cpr:
            return '✅ Good Performer'
        return '📈 Average'

    def validate_ad_totals(self, ad_tbl, summary):
        """Validation vs summary row - exact specification"""
        meta = {'spend': float(summary['Amount spent (USD)']),
                'reach': int(summary['Reach']),
                'impressions': int(summary['Impressions'])}

        ours = {'spend': ad_tbl.spend.sum(),
                'reach': ad_tbl.reach.sum(),
                'impressions': ad_tbl.impressions.sum()}

        tol = 0.01  # $0.01 / 1 unit tolerance
        results = {}
        for k in meta:
            diff = ours[k] - meta[k]
            ok = abs(diff) <= tol if k == 'spend' else diff == 0
            results[k] = {'meta': meta[k], 'ours': ours[k], 'diff': diff, 'match': ok}
        overall = 'PASS' if all(r['match'] for r in results.values()) else 'FAIL'
        return overall, results

    def analyze_ads(self):
        """Driver function for ad analysis - exact specification"""
        try:
            if self.raw_data is None or self.summary_row is None:
                raise Exception("No data loaded for ad analysis")

            self.update_progress(85, "Running Ad Performance Analysis...")

            # Use existing data
            granular, summary = self.load_meta_csv_for_ads(self.raw_data, self.summary_row)
            granular = self.clean_granular_for_ads(granular)
            ad_tbl = self.build_ad_table(granular)

            # Calculate overall CPR and categorize
            overall_cpr = ad_tbl.spend.sum() / ad_tbl.results.sum() if ad_tbl.results.sum() else np.nan
            ad_tbl['category'] = ad_tbl.apply(lambda row: self.tag_category(row, overall_cpr), axis=1)

            # Validation
            val_status, val_detail = self.validate_ad_totals(ad_tbl, summary)

            # Store results
            self.ad_performance_table = ad_tbl
            self.ad_validation = {'status': val_status, 'detail': val_detail}

            # Generate insights
            self.ad_insights = {
                'top_spenders': ad_tbl.nlargest(5, 'spend').to_dict('records'),
                'efficient_ads': (ad_tbl[ad_tbl.spend >= 50]
                                  .nsmallest(5, 'cost_per_result')
                                  .to_dict('records')),
                'overall_cost_per_result': overall_cpr,
                'category_counts': ad_tbl.category.value_counts().to_dict()
            }

            self.update_progress(95, "Ad Performance Analysis completed")
            return ad_tbl, val_status

        except Exception as e:
            self.handle_error("Error in ad performance analysis", e)
            return None, "ERROR"

    def safe_numeric_extract(self, row, column):
        """Safely extract numeric value from summary row"""
        try:
            value = row.get(column, 0)
            if pd.isna(value) or value == '' or str(value).lower() in ['nan', 'none']:
                return 0
            return float(str(value).replace(',', ''))
        except:
            return 0
    
    def determine_overall_status(self, validation_results):
        """Determine overall validation status with special consideration for reach deduplication"""
        statuses = [result['status'] for result in validation_results.values()]

        # Count actual failures (excluding expected reach differences)
        critical_failures = []
        for metric, result in validation_results.items():
            if result['status'] == 'FAIL':
                # Reach discrepancies are expected due to deduplication, not critical
                if metric != 'reach':
                    critical_failures.append(metric)

        if len(critical_failures) > 0:
            return 'FAIL'
        elif any(status == 'WARNING' for status in statuses):
            return 'WARNING'
        else:
            return 'PASS'
    
    def update_validation_displays(self):
        """Update the modern validation displays"""
        if not hasattr(self, 'accuracy_check') or not self.accuracy_check:
            return

        meta_totals = self.accuracy_check['meta_totals']
        our_totals = self.accuracy_check['our_totals']
        validation_results = self.accuracy_check['validation_results']
        overall_status = self.accuracy_check['overall_status']

        # Update compact metric cards
        for metric, card in self.metric_cards.items():
            if metric in validation_results:
                result = validation_results[metric]
                meta_val = result['meta_value']
                status = result['status']

                # Format values
                if metric == 'spend':
                    meta_text = f"${meta_val:,.0f}"
                else:
                    meta_text = f"{meta_val:,.0f}"

                # Update card values
                card.meta_value.config(text=meta_text)

                # Update status with color coding
                if status == 'PASS':
                    status_text = "✅"
                    status_color = self.colors['success']
                elif status == 'WARNING':
                    status_text = "⚠️"
                    status_color = self.colors['warning']
                else:
                    status_text = "❌"
                    status_color = self.colors['danger']

                card.status_label.config(text=status_text, foreground=status_color)

        # Compact layout - no detailed displays needed here
        # All details are available in the tabs below

        # Update overall status
        if overall_status == 'PASS':
            status_icon = "✅"
            status_msg = "VALIDATION PASSED"
            status_style = 'Success.TLabel'
        elif overall_status == 'WARNING':
            status_icon = "⚠️"
            status_msg = "VALIDATION WARNING"
            status_style = 'Warning.TLabel'
        else:
            status_icon = "❌"
            status_msg = "VALIDATION FAILED"
            status_style = 'Error.TLabel'

        self.validation_status_var.set(f"{status_icon} {status_msg}")
        self.validation_status_label.config(style=status_style)

    def update_ad_performance_display(self):
        """Update the ad performance analysis displays"""
        if self.ad_performance_table is None:
            return

        # Clear existing data
        for item in self.ad_performance_tree.get_children():
            self.ad_performance_tree.delete(item)

        # Populate ad performance table
        for _, row in self.ad_performance_table.iterrows():
            values = (
                row['ad_name'],
                row['campaign_name'],
                f"${row['spend']:.2f}",
                f"{row['results']:.0f}",
                f"{row['reach']:.0f}",
                f"{row['impressions']:.0f}",
                f"{row['frequency']:.2f}",
                f"${row['cost_per_result']:.2f}" if not pd.isna(row['cost_per_result']) else "N/A",
                row['status'],
                row['start_date'].strftime('%Y-%m-%d') if hasattr(row['start_date'], 'strftime') else str(row['start_date']),
                row['end_date'].strftime('%Y-%m-%d') if hasattr(row['end_date'], 'strftime') else str(row['end_date']),
                f"{row['days_active']:.0f}",
                row['category']
            )
            self.ad_performance_tree.insert('', tk.END, values=values)

        # Update insights display
        if self.ad_insights:
            insights_report = []
            insights_report.append("💡 AD PERFORMANCE INSIGHTS")
            insights_report.append("=" * 50)
            insights_report.append("")

            # Overall metrics
            insights_report.append(f"📊 OVERALL METRICS")
            insights_report.append(f"   Overall Cost per Result: ${self.ad_insights['overall_cost_per_result']:.2f}")
            insights_report.append(f"   Total Ads Analyzed: {len(self.ad_performance_table)}")
            insights_report.append("")

            # Category breakdown
            insights_report.append("📈 PERFORMANCE CATEGORIES")
            for category, count in self.ad_insights['category_counts'].items():
                insights_report.append(f"   {category}: {count} ads")
            insights_report.append("")

            # Top spenders
            insights_report.append("💰 TOP 5 SPENDERS")
            for i, ad in enumerate(self.ad_insights['top_spenders'], 1):
                insights_report.append(f"   {i}. {ad['ad_name']}")
                insights_report.append(f"      Spend: ${ad['spend']:.2f} | Results: {ad['results']:.0f} | CPR: ${ad['cost_per_result']:.2f}")
            insights_report.append("")

            # Most efficient ads
            insights_report.append("🎯 TOP 5 MOST EFFICIENT ADS (Spend ≥ $50)")
            for i, ad in enumerate(self.ad_insights['efficient_ads'], 1):
                insights_report.append(f"   {i}. {ad['ad_name']}")
                insights_report.append(f"      CPR: ${ad['cost_per_result']:.2f} | Spend: ${ad['spend']:.2f} | Results: {ad['results']:.0f}")
            insights_report.append("")

            # Validation status
            if self.ad_validation:
                insights_report.append("✅ AD-LEVEL VALIDATION")
                insights_report.append(f"   Status: {self.ad_validation['status']}")
                for metric, details in self.ad_validation['detail'].items():
                    match_icon = "✅" if details['match'] else "❌"
                    insights_report.append(f"   {match_icon} {metric.title()}: Meta={details['meta']:,.2f}, Ours={details['ours']:,.2f}, Diff={details['diff']:+,.2f}")

            self.ad_insights_display.config(state=tk.NORMAL)
            self.ad_insights_display.delete(1.0, tk.END)
            self.ad_insights_display.insert(1.0, "\n".join(insights_report))
            self.ad_insights_display.config(state=tk.DISABLED)

        # Update the new insights panel
        self.update_insights_panel()

    def update_insights_panel(self):
        """Update the compact insights panel with visual data"""
        if not self.ad_insights or self.ad_performance_table is None:
            return

        # Update overall metrics
        self.overall_cpr_var.set(f"${self.ad_insights['overall_cost_per_result']:.2f}")
        self.total_ads_var.set(f"{len(self.ad_performance_table):,} ads")

        # Update top spender
        if self.ad_insights['top_spenders']:
            top_spender = self.ad_insights['top_spenders'][0]
            spender_text = f"{top_spender['ad_name'][:20]}... (${top_spender['spend']:,.0f})"
            self.top_spender_var.set(spender_text)

        # Update most efficient
        if self.ad_insights['efficient_ads']:
            efficient = self.ad_insights['efficient_ads'][0]
            efficient_text = f"{efficient['ad_name'][:20]}... (${efficient['cost_per_result']:.2f})"
            self.most_efficient_var.set(efficient_text)

        # Update categories summary
        top_categories = list(self.ad_insights['category_counts'].items())[:3]
        categories_summary = " | ".join([f"{cat.split()[0]}: {count}" for cat, count in top_categories])
        self.categories_summary_var.set(categories_summary)

        # Ad validation status is now shown in the main validation status

        # Compact layout - detailed insights are available in the tabs

    def update_file_info_display(self):
        """Update the file info display in the sidebar"""
        if hasattr(self, 'data_quality_report') and self.data_quality_report:
            info = self.data_quality_report
            file_info = f"📁 {info.get('source_file', 'Unknown')}\n"
            file_info += f"📊 {info.get('granular_rows', 0):,} granular records\n"
            file_info += f"📋 {info.get('summary_rows', 0)} summary row\n"
            file_info += f"📅 {info.get('load_timestamp', 'Unknown').strftime('%Y-%m-%d %H:%M') if hasattr(info.get('load_timestamp', ''), 'strftime') else 'Unknown'}"
            self.file_info_var.set(file_info)

    def update_summary_displays(self):
        """Update summary-related displays"""
        if self.summary_row is None:
            return
        
        # Update summary detail tab
        for item in self.summary_detail_tree.get_children():
            self.summary_detail_tree.delete(item)
        
        # Display all fields from summary row
        for field, value in self.summary_row.items():
            data_type = type(value).__name__
            
            # Determine notes based on field and value
            if pd.isna(value) or str(value).lower() in ['nan', 'none', '']:
                notes = "Missing/Empty - Normal for summary row"
            elif field in ['Amount spent (USD)', 'Results', 'Reach', 'Impressions']:
                notes = "Official Meta total - Use for validation"
            elif field in ['Reporting starts', 'Reporting ends']:
                notes = "Date range for this export"
            else:
                notes = "Metadata field"
            
            # Format value for display
            if pd.isna(value):
                display_value = "N/A"
            elif isinstance(value, (int, float)) and not pd.isna(value):
                if field == 'Amount spent (USD)':
                    display_value = f"${value:,.2f}"
                else:
                    display_value = f"{value:,.0f}"
            else:
                display_value = str(value)
            
            values = (field, display_value, data_type, notes)
            self.summary_detail_tree.insert('', 'end', values=values)
    
    def prepare_airtable_export(self):
        """Prepare data specifically optimized for Airtable import"""
        try:
            source_data = self.cleaned_data if self.cleaned_data is not None else self.granular_data
            
            # Create Airtable-optimized structure
            airtable_data = source_data.copy()
            
            # Generate unique ID for each record
            airtable_data['unique_id'] = airtable_data.apply(
                lambda row: hashlib.md5(
                    f"{row.get('reporting_date', '')}{row.get('ad_name', '')}{row.get('campaign_name', '')}".encode()
                ).hexdigest()[:12], axis=1
            )
            
            # Ensure all required columns exist with proper types
            required_columns = {
                'unique_id': str,
                'date': 'datetime64[ns]',
                'month': str,
                'ad_name': str,
                'campaign_name': str,
                'spend_usd': float,
                'results': float,
                'reach': int,
                'impressions': int,
                'frequency': float,
                'status': str
            }
            
            # Map and create columns
            column_mapping = {
                'reporting_date': 'date',
                'amount_spent_usd': 'spend_usd',
                'ad_status': 'status'
            }
            
            for old_col, new_col in column_mapping.items():
                if old_col in airtable_data.columns:
                    airtable_data[new_col] = airtable_data[old_col]
            
            # Ensure all required columns exist
            for col, dtype in required_columns.items():
                if col not in airtable_data.columns:
                    if dtype == str:
                        airtable_data[col] = 'Unknown'
                    elif dtype == float:
                        airtable_data[col] = 0.0
                    elif dtype == int:
                        airtable_data[col] = 0
                    elif 'datetime' in str(dtype):
                        airtable_data[col] = pd.NaT
            
            # Select and order columns for Airtable
            final_columns = list(required_columns.keys())
            airtable_export = airtable_data[final_columns].copy()
            
            # Add metadata columns
            airtable_export['source_file'] = self.data_quality_report.get('source_file', 'unknown')
            airtable_export['processed_at'] = self.processing_timestamp
            airtable_export['app_version'] = self.app_version
            airtable_export['validation_status'] = self.accuracy_check.get('overall_status', 'Not Validated')
            
            # Final data type enforcement
            airtable_export['spend_usd'] = pd.to_numeric(airtable_export['spend_usd'], errors='coerce').fillna(0)
            airtable_export['results'] = pd.to_numeric(airtable_export['results'], errors='coerce').fillna(0)
            airtable_export['reach'] = pd.to_numeric(airtable_export['reach'], errors='coerce').fillna(0).astype(int)
            airtable_export['impressions'] = pd.to_numeric(airtable_export['impressions'], errors='coerce').fillna(0).astype(int)
            airtable_export['frequency'] = pd.to_numeric(airtable_export['frequency'], errors='coerce').fillna(0)
            
            return airtable_export
            
        except Exception as e:
            raise Exception(f"Error preparing Airtable export: {str(e)}")
    
    def update_progress(self, value, message):
        self.progress_var.set(value)
        self.status_var.set(message)
        self.root.update()
    
    def update_airtable_display(self):
        # Clear existing data
        for item in self.airtable_tree.get_children():
            self.airtable_tree.delete(item)
        
        if self.airtable_export is not None:
            # Show first 100 rows
            display_data = self.airtable_export.head(100)
            for _, row in display_data.iterrows():
                values = (
                    str(row['unique_id'])[:12],
                    row['date'].strftime('%Y-%m-%d') if pd.notnull(row['date']) else 'N/A',
                    str(row['month']),
                    str(row['ad_name'])[:20],
                    str(row['campaign_name'])[:20],
                    f"${row['spend_usd']:.2f}",
                    f"{row['results']:.0f}",
                    f"{row['reach']:,}",
                    f"{row['impressions']:,}",
                    f"{row['frequency']:.2f}",
                    str(row['status'])
                )
                self.airtable_tree.insert('', 'end', values=values)
    
    def update_monthly_display(self):
        # Clear existing data
        for item in self.monthly_tree.get_children():
            self.monthly_tree.delete(item)
        
        if self.monthly_summary is not None:
            for _, row in self.monthly_summary.iterrows():
                values = (
                    row['period'],
                    f"${row['total_spend']:,.2f}",
                    f"{row['total_results']:,.0f}",
                    f"{row['entry_count']:,}",
                    f"{row['avg_frequency']:.2f}",
                    row.get('data_quality_score', 'N/A')
                )
                self.monthly_tree.insert('', 'end', values=values)
    
    def update_daily_display(self):
        # Clear existing data
        for item in self.daily_tree.get_children():
            self.daily_tree.delete(item)
        
        if self.daily_aggregated is not None:
            # Show first 100 rows
            display_data = self.daily_aggregated.head(100)
            for _, row in display_data.iterrows():
                values = (
                    row['period'],
                    f"${row['total_spend']:,.2f}",
                    f"{row['total_results']:,.0f}",
                    f"{row['total_reach']:,.0f}",
                    f"{row['total_impressions']:,.0f}",
                    f"{row['frequency']:.2f}"
                )
                self.daily_tree.insert('', 'end', values=values)
    
    def update_cleaned_display(self):
        # Clear existing data
        for item in self.cleaned_tree.get_children():
            self.cleaned_tree.delete(item)
        
        if self.cleaned_data is not None:
            # Show first 100 rows
            display_data = self.cleaned_data.head(100)
            for _, row in display_data.iterrows():
                values = (
                    row['reporting_date'].strftime('%Y-%m-%d') if pd.notnull(row['reporting_date']) else 'N/A',
                    str(row.get('ad_name', 'N/A'))[:20],
                    str(row.get('campaign_name', 'N/A'))[:20],
                    f"${row.get('amount_spent_usd', 0):,.2f}",
                    f"{row.get('results', 0):,.0f}",
                    f"{row.get('reach', 0):,.0f}",
                    f"{row.get('impressions', 0):,.0f}",
                    str(row.get('ad_status', 'Unknown'))
                )
                self.cleaned_tree.insert('', 'end', values=values)
    
    def update_raw_data_display(self):
        # Clear existing data
        for item in self.raw_tree.get_children():
            self.raw_tree.delete(item)
        
        if self.raw_data is not None:
            # Show first 100 rows
            display_data = self.raw_data.head(100)
            for idx, row in display_data.iterrows():
                # Check for issues in this row
                issues = []
                if pd.isna(row.get('Reporting ends')):
                    issues.append('Invalid Date')
                if row.get('Amount spent (USD)', 0) == 0 and row.get('Reach', 0) == 0:
                    issues.append('No Activity')
                
                values = (
                    str(row.get('Reporting ends', 'Invalid'))[:10],
                    str(row.get('Ad name', 'N/A'))[:20],
                    f"${row.get('Amount spent (USD)', 0):,.2f}",
                    f"{row.get('Results', 0):,.0f}",
                    f"{row.get('Reach', 0):,.0f}",
                    f"{row.get('Impressions', 0):,.0f}",
                    ', '.join(issues) if issues else 'OK'
                )
                self.raw_tree.insert('', 'end', values=values)
    
    def generate_quality_report(self):
        if self.raw_data is None:
            return
        
        try:
            report = []
            report.append("╔══════════════════════════════════════╗")
            report.append("║     DATA QUALITY & PROCESSING       ║")
            report.append("╚══════════════════════════════════════╝")
            report.append("")
            
            # File information
            report.append("📁 FILE INFORMATION")
            report.append(f"   Source: {self.data_quality_report.get('source_file', 'Unknown')}")
            report.append(f"   Total Rows: {self.data_quality_report.get('original_rows', 0):,}")
            report.append(f"   Summary Rows: {self.data_quality_report.get('summary_rows', 0):,}")
            report.append(f"   Granular Rows: {self.data_quality_report.get('granular_rows', 0):,}")
            report.append("")
            
            # Summary row info
            if self.summary_row is not None:
                report.append("📊 SUMMARY ROW DETECTED")
                report.append("   ✅ Meta's official totals extracted")
                report.append("   ✅ Available for validation")
                report.append("   ✅ Will be used for accuracy checks")
            else:
                report.append("⚠️  NO SUMMARY ROW DETECTED")
                report.append("   ⚠️  Cannot perform validation")
                report.append("   ⚠️  Manual verification recommended")
            report.append("")
            
            # Data processing status
            if hasattr(self, 'accuracy_check') and self.accuracy_check:
                report.append("🔍 VALIDATION STATUS")
                overall_status = self.accuracy_check.get('overall_status', 'Unknown')
                if overall_status == 'PASS':
                    report.append("   ✅ VALIDATION PASSED")
                    report.append("   ✅ Data matches Meta's totals")
                    report.append("   ✅ Safe to export and use")
                elif overall_status == 'WARNING':
                    report.append("   ⚠️  VALIDATION WARNING")
                    report.append("   ⚠️  Minor discrepancies found")
                    report.append("   ⚠️  Review validation panel")
                else:
                    report.append("   ❌ VALIDATION FAILED")
                    report.append("   ❌ Significant discrepancies")
                    report.append("   ❌ Do not use until resolved")
            else:
                report.append("⏳ VALIDATION PENDING")
                report.append("   Process data to run validation")
            
            report.append("")
            
            # Processing recommendations
            report.append("💡 RECOMMENDATIONS")
            if self.summary_row is not None:
                report.append("   ✅ Use summary row for dashboard cards")
                report.append("   ✅ Validate all aggregations against summary")
                report.append("   ✅ Export granular data for detailed analysis")
            else:
                report.append("   ⚠️  Manually verify totals with Meta Ads Manager")
                report.append("   ⚠️  Consider re-exporting with summary row")
            
            if hasattr(self, 'validation_errors') and self.validation_errors:
                report.append("")
                report.append("🔧 DATA CLEANING ACTIONS")
                error_types = {}
                for error in self.validation_errors:
                    error_type = error.get('error_type', 'Unknown')
                    error_types[error_type] = error_types.get(error_type, 0) + 1
                
                for error_type, count in error_types.items():
                    report.append(f"   • {error_type}: {count} issues resolved")
            
            # Display the report
            self.quality_text.config(state=tk.NORMAL)
            self.quality_text.delete(1.0, tk.END)
            self.quality_text.insert(1.0, "\n".join(report))
            self.quality_text.config(state=tk.DISABLED)
            
        except Exception as e:
            self.handle_error("Error generating quality report", e)
    
    def export_airtable(self):
        if self.airtable_export is None:
            messagebox.showwarning("No Data", "Please process data first")
            return
        
        try:
            file_path = filedialog.asksaveasfilename(
                title="Save Airtable Export",
                defaultextension=".csv",
                filetypes=[("CSV files", "*.csv"), ("All files", "*.*")]
            )
            
            if file_path:
                # Add validation status to filename if failed
                validation_status = self.accuracy_check.get('overall_status', 'Unknown')
                if validation_status == 'FAIL':
                    base, ext = os.path.splitext(file_path)
                    file_path = f"{base}_VALIDATION_FAILED{ext}"
                    messagebox.showwarning("Validation Failed", 
                                         "File saved with VALIDATION_FAILED suffix. Do not use until issues are resolved.")
                
                self.airtable_export.to_csv(file_path, index=False)
                messagebox.showinfo("Export Complete", f"Airtable data exported to:\n{file_path}")
                
        except Exception as e:
            self.handle_error("Error exporting Airtable data", e)
    
    def export_daily(self):
        if self.daily_aggregated is None:
            messagebox.showwarning("No Data", "Please process data first")
            return
        
        try:
            file_path = filedialog.asksaveasfilename(
                title="Save Daily Aggregated Data",
                defaultextension=".csv",
                filetypes=[("CSV files", "*.csv"), ("All files", "*.*")]
            )
            
            if file_path:
                self.daily_aggregated.to_csv(file_path, index=False)
                messagebox.showinfo("Export Complete", f"Daily data exported to:\n{file_path}")
                
        except Exception as e:
            self.handle_error("Error exporting daily data", e)
    
    def export_monthly(self):
        if self.monthly_summary is None:
            messagebox.showwarning("No Data", "Please process data first")
            return
        
        try:
            file_path = filedialog.asksaveasfilename(
                title="Save Monthly Summary",
                defaultextension=".csv",
                filetypes=[("CSV files", "*.csv"), ("All files", "*.*")]
            )
            
            if file_path:
                self.monthly_summary.to_csv(file_path, index=False)
                messagebox.showinfo("Export Complete", f"Monthly summary exported to:\n{file_path}")
                
        except Exception as e:
            self.handle_error("Error exporting monthly data", e)
    
    def export_validation_report(self):
        """Export comprehensive validation and quality report"""
        try:
            file_path = filedialog.asksaveasfilename(
                title="Save Validation Report",
                defaultextension=".json",
                filetypes=[("JSON files", "*.json"), ("Text files", "*.txt"), ("All files", "*.*")]
            )
            
            if not file_path:
                return
            
            # Prepare comprehensive report
            report_data = {
                'app_info': {
                    'version': self.app_version,
                    'processing_timestamp': self.processing_timestamp.isoformat() if self.processing_timestamp else None
                },
                'file_info': self.data_quality_report,
                'summary_row_data': self.summary_row.to_dict() if self.summary_row is not None else None,
                'accuracy_validation': self.accuracy_check,
                'validation_errors': self.validation_errors,
                'data_statistics': {
                    'granular_records': len(self.granular_data) if self.granular_data is not None else 0,
                    'cleaned_records': len(self.cleaned_data) if self.cleaned_data is not None else 0,
                    'airtable_records': len(self.airtable_export) if self.airtable_export is not None else 0
                }
            }
            
            # Convert datetime objects to strings for JSON serialization
            def convert_datetime(obj):
                if isinstance(obj, datetime):
                    return obj.isoformat()
                elif isinstance(obj, pd.Timestamp):
                    return obj.isoformat()
                elif isinstance(obj, dict):
                    return {k: convert_datetime(v) for k, v in obj.items()}
                elif isinstance(obj, list):
                    return [convert_datetime(item) for item in obj]
                return obj
            
            report_data = convert_datetime(report_data)
            
            if file_path.endswith('.json'):
                with open(file_path, 'w') as f:
                    json.dump(report_data, f, indent=2, default=str)
            else:
                # Text format
                with open(file_path, 'w') as f:
                    f.write("META ADS DATA VALIDATION REPORT\n")
                    f.write("=" * 50 + "\n\n")
                    
                    f.write(f"Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                    f.write(f"App Version: {self.app_version}\n\n")
                    
                    if self.accuracy_check:
                        f.write("VALIDATION RESULTS:\n")
                        f.write("-" * 20 + "\n")
                        overall_status = self.accuracy_check.get('overall_status', 'Unknown')
                        f.write(f"Overall Status: {overall_status}\n\n")
                        
                        for metric, result in self.accuracy_check.get('validation_results', {}).items():
                            f.write(f"{metric.upper()}:\n")
                            f.write(f"  Meta Value: {result['meta_value']:,.2f}\n")
                            f.write(f"  Our Value: {result['our_value']:,.2f}\n")
                            f.write(f"  Difference: {result['difference']:+,.2f}\n")
                            f.write(f"  Status: {result['status']}\n")
                            f.write(f"  Note: {result['note']}\n\n")
            
            messagebox.showinfo("Export Complete", f"Validation report exported to:\n{file_path}")
            
        except Exception as e:
            self.handle_error("Error exporting validation report", e)

    def export_ad_performance(self):
        """Export ad performance table to CSV - exact specification"""
        try:
            if self.ad_performance_table is None:
                messagebox.showwarning("No Data", "No ad performance data to export. Please process data first.")
                return

            filename = filedialog.asksaveasfilename(
                defaultextension=".csv",
                filetypes=[("CSV files", "*.csv")],
                title="Save Ad Performance Data",
                initialvalue="ad_performance.csv"
            )

            if filename:
                # Export with exact column specification
                self.ad_performance_table.to_csv(filename, index=False)
                messagebox.showinfo("Export Complete", f"Ad performance data exported to:\n{filename}")
                self.status_var.set(f"✅ Ad performance exported to {os.path.basename(filename)}")

        except Exception as e:
            self.handle_error("Error exporting ad performance", e)

    def export_ad_insights(self):
        """Export ad insights to JSON - exact specification"""
        try:
            if self.ad_insights is None:
                messagebox.showwarning("No Data", "No ad insights data to export. Please process data first.")
                return

            filename = filedialog.asksaveasfilename(
                defaultextension=".json",
                filetypes=[("JSON files", "*.json")],
                title="Save Ad Insights",
                initialvalue="insights.json"
            )

            if filename:
                with open(filename, 'w') as f:
                    json.dump(self.ad_insights, f, indent=2, default=str)
                messagebox.showinfo("Export Complete", f"Ad insights exported to:\n{filename}")
                self.status_var.set(f"✅ Ad insights exported to {os.path.basename(filename)}")

                # Also export validation.json if available
                if self.ad_validation:
                    validation_filename = filename.replace('insights.json', 'validation.json')
                    with open(validation_filename, 'w') as f:
                        json.dump(self.ad_validation, f, indent=2, default=str)
                    messagebox.showinfo("Additional Export", f"Ad validation also exported to:\n{validation_filename}")

        except Exception as e:
            self.handle_error("Error exporting ad insights", e)

    def handle_error(self, title, error):
        error_msg = f"{title}:\n{str(error)}\n\nTraceback:\n{traceback.format_exc()}"
        messagebox.showerror(title, error_msg)
        print(f"ERROR: {error_msg}")

def main():
    root = tk.Tk()
    app = MetaAdsTransformerPro(root)
    root.mainloop()

if __name__ == "__main__":
    main()